{% extends "base.html" %}

{% block title %}Parse Results - Debi<PERSON>heck Output Reader{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-line me-2"></i>
                Parse Results
            </h2>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="fas fa-upload me-2"></i>
                Upload Another File
            </a>
        </div>
    </div>
</div>

{% if result.error %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Error Processing File</h5>
            <p class="mb-0">{{ result.error }}</p>
            {% if result.raw_content %}
            <details class="mt-3">
                <summary>Raw File Content (first 500 characters)</summary>
                <pre class="mt-2 p-2 bg-light border rounded"><code>{{ result.raw_content }}</code></pre>
            </details>
            {% endif %}
        </div>
    </div>
</div>
{% else %}

<!-- Output Type Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    DebiCheck Output File Analysis
                </h5>
            </div>
            <div class="card-body">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mb-3" id="outputTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if result.file_type in ['OUTPUT_1ST', 'STATUS_REPORT_1ST'] %}active{% endif %}"
                                id="output1-tab" data-bs-toggle="tab" data-bs-target="#output1" type="button" role="tab">
                            <i class="fas fa-file-export me-1"></i>
                            1st Output File
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if result.file_type in ['OUTPUT_2ND', 'STATUS_REPORT_2ND'] %}active{% endif %}"
                                id="output2-tab" data-bs-toggle="tab" data-bs-target="#output2" type="button" role="tab">
                            <i class="fas fa-file-import me-1"></i>
                            2nd Output File
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if result.file_type in ['OUTPUT_3RD', 'MANDATE_ACCEPTED'] %}active{% endif %}"
                                id="output3-tab" data-bs-toggle="tab" data-bs-target="#output3" type="button" role="tab">
                            <i class="fas fa-file-check me-1"></i>
                            3rd Output File
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="outputTabsContent">
                    <!-- 1st Output Tab -->
                    <div class="tab-pane fade {% if result.file_type in ['OUTPUT_1ST', 'STATUS_REPORT_1ST'] %}show active{% endif %}"
                         id="output1" role="tabpanel">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>1st Output File - From Absa Payments System</h6>
                            <p class="mb-0">This file originates from the Absa Payments System and is sent back to the Corporate Client via the Link Direct Corporate Channel.</p>
                        </div>
                        {% if result.file_type in ['OUTPUT_1ST', 'STATUS_REPORT_1ST'] %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Current File Type:</strong> {{ result.file_type_description }}
                        </div>
                        {% else %}
                        <div class="alert alert-secondary">
                            <i class="fas fa-times-circle me-2"></i>
                            This file is not identified as a 1st Output File.
                        </div>
                        {% endif %}
                    </div>

                    <!-- 2nd Output Tab -->
                    <div class="tab-pane fade {% if result.file_type in ['OUTPUT_2ND', 'STATUS_REPORT_2ND'] %}show active{% endif %}"
                         id="output2" role="tabpanel">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-info-circle me-2"></i>2nd Output File - From Debtor's Bank</h6>
                            <p class="mb-0">This file comes from the Debtor's Bank and is sent to the Corporate Client.</p>
                        </div>
                        {% if result.file_type in ['OUTPUT_2ND', 'STATUS_REPORT_2ND'] %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Current File Type:</strong> {{ result.file_type_description }}
                        </div>
                        {% else %}
                        <div class="alert alert-secondary">
                            <i class="fas fa-times-circle me-2"></i>
                            This file is not identified as a 2nd Output File.
                        </div>
                        {% endif %}
                    </div>

                    <!-- 3rd Output Tab -->
                    <div class="tab-pane fade {% if result.file_type in ['OUTPUT_3RD', 'MANDATE_ACCEPTED'] %}show active{% endif %}"
                         id="output3" role="tabpanel">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-info-circle me-2"></i>3rd Output File - Mandate Acceptance Report</h6>
                            <p class="mb-0">This is specifically the Mandate Acceptance Report, which is provided to the Corporate Client. This report is specific to 0227 DebiCheck mandates (and 0997 for RM mandates for amendments) and indicates whether a mandate was successfully authenticated by the debtor.</p>
                        </div>
                        {% if result.file_type in ['OUTPUT_3RD', 'MANDATE_ACCEPTED'] %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Current File Type:</strong> {{ result.file_type_description }}
                        </div>
                        {% else %}
                        <div class="alert alert-secondary">
                            <i class="fas fa-times-circle me-2"></i>
                            This file is not identified as a 3rd Output File (Mandate Acceptance Report).
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info me-2"></i>
                    File Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Filename:</strong> {{ filename }}</p>
                        <p><strong>Detected Type:</strong> {{ result.file_type_description }}</p>
                    </div>
                    <div class="col-md-6">
                        {% if result.header.date %}
                        <p><strong>Date:</strong> {{ result.header.date }}</p>
                        {% endif %}
                        {% if result.header.time %}
                        <p><strong>Time:</strong> {{ result.header.time }}</p>
                        {% endif %}
                    </div>
                </div>
                {% if result.output_description %}
                <div class="row">
                    <div class="col-12">
                        <p><strong>Description:</strong> {{ result.output_description }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card summary-card">
            <div class="card-header">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-chart-pie me-2"></i>
                    Summary Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% if result.file_type == 'MANDATE_ACCEPTED' %}
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-success">{{ result.summary.total_mandates }}</h3>
                                <p class="mb-0">Total Mandates</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-primary">{{ result.summary.accepted_mandates }}</h3>
                                <p class="mb-0">Accepted</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-warning">{{ result.summary.rejected_mandates }}</h3>
                                <p class="mb-0">Rejected</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-info">R{{ "%.2f"|format(result.summary.total_maximum_amount) }}</h3>
                                <p class="mb-0">Total Max Amount</p>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-success">{{ result.summary.total_records }}</h3>
                                <p class="mb-0">Total Records</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-primary">{{ result.summary.successful_records }}</h3>
                                <p class="mb-0">Successful</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-danger">{{ result.summary.failed_records }}</h3>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-info">R{{ "%.2f"|format(result.summary.total_amount) }}</h3>
                                <p class="mb-0">Total Amount</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Breakdown -->
{% if result.summary.status_breakdown %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Status Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for status, count in result.summary.status_breakdown.items() %}
                    <div class="col-md-4 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                            <span><strong>{{ status }}</strong></span>
                            <span class="badge bg-primary">{{ count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Detailed Records -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    {% if result.file_type == 'MANDATE_ACCEPTED' %}
                    Mandate Records
                    {% else %}
                    Transaction Records
                    {% endif %}
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleRawData()">
                    <i class="fas fa-code me-1"></i>
                    Toggle Raw Data
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    {% if result.file_type == 'MANDATE_ACCEPTED' %}
                    <!-- Mandate Records Table -->
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Line</th>
                                <th>Mandate Ref</th>
                                <th>Creditor Ref</th>
                                <th>Debtor Account</th>
                                <th>Debtor Name</th>
                                <th>Max Amount</th>
                                <th>Status</th>
                                <th>Acceptance Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mandate in result.mandates %}
                            <tr class="{% if mandate.error %}error-record{% elif mandate.mandate_status and mandate.mandate_status.upper() in ['ACCEPTED', 'ACTIVE', 'APPROVED'] %}success-record{% endif %}">
                                <td>{{ mandate.line_number }}</td>
                                <td>{{ mandate.mandate_reference or '-' }}</td>
                                <td>{{ mandate.creditor_reference or '-' }}</td>
                                <td>{{ mandate.debtor_account or '-' }}</td>
                                <td>{{ mandate.debtor_name or '-' }}</td>
                                <td>{{ mandate.maximum_amount or '-' }}</td>
                                <td>
                                    {% if mandate.error %}
                                    <span class="badge bg-danger">Error</span>
                                    {% else %}
                                    <span class="badge {% if mandate.mandate_status and mandate.mandate_status.upper() in ['ACCEPTED', 'ACTIVE', 'APPROVED'] %}bg-success{% else %}bg-warning{% endif %}">
                                        {{ mandate.mandate_status or 'Unknown' }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>{{ mandate.acceptance_date or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <!-- Transaction Records Table -->
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Transaction Ref</th>
                                <th>Debtor Details</th>
                                <th>Creditor</th>
                                <th>Amount</th>
                                <th>Collection Date</th>
                                <th>Status</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in result.records %}
                            <tr class="{% if record.transaction_status == 'SUCCESS' %}table-success{% elif record.transaction_status == 'FAILED' %}table-danger{% else %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ record.transaction_reference or 'N/A' }}</strong><br>
                                    <small class="text-muted">Mandate: {{ record.mandate_reference or 'N/A' }}</small>
                                </td>
                                <td>
                                    <strong>{{ record.debtor_full_name }}</strong><br>
                                    <small class="text-muted">
                                        Account: {{ record.debtor_account or 'N/A' }}<br>
                                        ID: {{ record.debtor_id or 'N/A' }}
                                    </small>
                                </td>
                                <td>
                                    <strong>{{ record.creditor_name or 'N/A' }}</strong><br>
                                    <small class="text-muted">Ref: {{ record.creditor_reference or 'N/A' }}</small>
                                </td>
                                <td>
                                    <strong class="{% if record.transaction_status == 'SUCCESS' %}text-success{% elif record.transaction_status == 'FAILED' %}text-danger{% else %}text-warning{% endif %}">
                                        {{ record.formatted_amount }}
                                    </strong><br>
                                    <small class="text-muted">{{ record.currency or 'ZAR' }}</small>
                                </td>
                                <td>
                                    {% if record.collection_date_formatted and record.collection_date_formatted != 'N/A' %}
                                        {{ record.collection_date_formatted }}
                                    {% else %}
                                        {{ record.processing_date_formatted or 'N/A' }}
                                    {% endif %}
                                    {% if record.due_date_formatted and record.due_date_formatted != 'N/A' %}
                                        <br><small class="text-muted">Due: {{ record.due_date_formatted }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ record.status_class }}">
                                        {{ record.transaction_status }}
                                    </span>
                                    {% if record.tracking_reference and record.tracking_reference != 'N/A' %}
                                        <br><small class="text-muted">Track: {{ record.tracking_reference }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.reason_code and record.reason_code != 'N/A' %}
                                        <strong>{{ record.reason_code }}</strong><br>
                                    {% endif %}
                                    <small>{{ record.reason_detail or record.status_description or 'N/A' }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Raw Data Section (Hidden by default) -->
<div class="row mt-4" id="rawDataSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-code me-2"></i>
                    Raw File Content
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 border rounded" style="max-height: 400px; overflow-y: auto;"><code>{{ result.raw_content }}</code></pre>
            </div>
        </div>
    </div>
</div>

{% endif %}
{% endblock %}

{% block scripts %}
<script>
function toggleRawData() {
    const rawDataSection = document.getElementById('rawDataSection');
    if (rawDataSection.style.display === 'none') {
        rawDataSection.style.display = 'block';
    } else {
        rawDataSection.style.display = 'none';
    }
}
</script>
{% endblock %}
