{% extends "base.html" %}

{% block title %}Parse Results - Debi<PERSON>heck Output Reader{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-line me-2"></i>
                Parse Results
            </h2>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="fas fa-upload me-2"></i>
                Upload Another File
            </a>
        </div>
    </div>
</div>

{% if result.error %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Error Processing File</h5>
            <p class="mb-0">{{ result.error }}</p>
            {% if result.raw_content %}
            <details class="mt-3">
                <summary>Raw File Content (first 500 characters)</summary>
                <pre class="mt-2 p-2 bg-light border rounded"><code>{{ result.raw_content }}</code></pre>
            </details>
            {% endif %}
        </div>
    </div>
</div>
{% else %}

<!-- File Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    File Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Filename:</strong> {{ filename }}</p>
                        <p><strong>File Type:</strong> {{ result.file_type_description }}</p>
                    </div>
                    <div class="col-md-6">
                        {% if result.header.date %}
                        <p><strong>Date:</strong> {{ result.header.date }}</p>
                        {% endif %}
                        {% if result.header.time %}
                        <p><strong>Time:</strong> {{ result.header.time }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card summary-card">
            <div class="card-header">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-chart-pie me-2"></i>
                    Summary Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% if result.file_type == 'MANDATE_ACCEPTED' %}
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-success">{{ result.summary.total_mandates }}</h3>
                                <p class="mb-0">Total Mandates</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-primary">{{ result.summary.accepted_mandates }}</h3>
                                <p class="mb-0">Accepted</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-warning">{{ result.summary.rejected_mandates }}</h3>
                                <p class="mb-0">Rejected</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-info">R{{ "%.2f"|format(result.summary.total_maximum_amount) }}</h3>
                                <p class="mb-0">Total Max Amount</p>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-success">{{ result.summary.total_records }}</h3>
                                <p class="mb-0">Total Records</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-primary">{{ result.summary.successful_records }}</h3>
                                <p class="mb-0">Successful</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-danger">{{ result.summary.failed_records }}</h3>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-light text-dark">
                            <div class="card-body">
                                <h3 class="text-info">R{{ "%.2f"|format(result.summary.total_amount) }}</h3>
                                <p class="mb-0">Total Amount</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Breakdown -->
{% if result.summary.status_breakdown %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Status Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for status, count in result.summary.status_breakdown.items() %}
                    <div class="col-md-4 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                            <span><strong>{{ status }}</strong></span>
                            <span class="badge bg-primary">{{ count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Detailed Records -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    {% if result.file_type == 'MANDATE_ACCEPTED' %}
                    Mandate Records
                    {% else %}
                    Transaction Records
                    {% endif %}
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleRawData()">
                    <i class="fas fa-code me-1"></i>
                    Toggle Raw Data
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    {% if result.file_type == 'MANDATE_ACCEPTED' %}
                    <!-- Mandate Records Table -->
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Line</th>
                                <th>Mandate Ref</th>
                                <th>Creditor Ref</th>
                                <th>Debtor Account</th>
                                <th>Debtor Name</th>
                                <th>Max Amount</th>
                                <th>Status</th>
                                <th>Acceptance Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mandate in result.mandates %}
                            <tr class="{% if mandate.error %}error-record{% elif mandate.mandate_status and mandate.mandate_status.upper() in ['ACCEPTED', 'ACTIVE', 'APPROVED'] %}success-record{% endif %}">
                                <td>{{ mandate.line_number }}</td>
                                <td>{{ mandate.mandate_reference or '-' }}</td>
                                <td>{{ mandate.creditor_reference or '-' }}</td>
                                <td>{{ mandate.debtor_account or '-' }}</td>
                                <td>{{ mandate.debtor_name or '-' }}</td>
                                <td>{{ mandate.maximum_amount or '-' }}</td>
                                <td>
                                    {% if mandate.error %}
                                    <span class="badge bg-danger">Error</span>
                                    {% else %}
                                    <span class="badge {% if mandate.mandate_status and mandate.mandate_status.upper() in ['ACCEPTED', 'ACTIVE', 'APPROVED'] %}bg-success{% else %}bg-warning{% endif %}">
                                        {{ mandate.mandate_status or 'Unknown' }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>{{ mandate.acceptance_date or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <!-- Status Records Table -->
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Line</th>
                                <th>Transaction Ref</th>
                                <th>Mandate Ref</th>
                                <th>Debtor Account</th>
                                <th>Amount</th>
                                <th>Status Code</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in result.records %}
                            <tr class="{% if record.error %}error-record{% elif record.status_code and record.status_code in ['00', '0', 'SUCCESS', 'ACCEPTED'] %}success-record{% endif %}">
                                <td>{{ record.line_number }}</td>
                                <td>{{ record.transaction_reference or '-' }}</td>
                                <td>{{ record.mandate_reference or '-' }}</td>
                                <td>{{ record.debtor_account or '-' }}</td>
                                <td>{{ record.amount or '-' }}</td>
                                <td>
                                    {% if record.error %}
                                    <span class="badge bg-danger">Error</span>
                                    {% else %}
                                    <span class="badge {% if record.status_code and record.status_code in ['00', '0', 'SUCCESS', 'ACCEPTED'] %}bg-success{% else %}bg-warning{% endif %}">
                                        {{ record.status_code or 'Unknown' }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>{{ record.status_description or record.error or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Raw Data Section (Hidden by default) -->
<div class="row mt-4" id="rawDataSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-code me-2"></i>
                    Raw File Content
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 border rounded" style="max-height: 400px; overflow-y: auto;"><code>{{ result.raw_content }}</code></pre>
            </div>
        </div>
    </div>
</div>

{% endif %}
{% endblock %}

{% block scripts %}
<script>
function toggleRawData() {
    const rawDataSection = document.getElementById('rawDataSection');
    if (rawDataSection.style.display === 'none') {
        rawDataSection.style.display = 'block';
    } else {
        rawDataSection.style.display = 'none';
    }
}
</script>
{% endblock %}
