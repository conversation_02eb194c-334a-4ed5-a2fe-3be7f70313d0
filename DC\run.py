#!/usr/bin/env python3
"""
DebiCheck Output Reader - Startup Script
Simple script to run the DebiCheck Output Reader application
"""

import os
import sys
import subprocess

def check_requirements():
    """Check if required packages are installed"""
    try:
        import flask
        print("✓ Flask is installed")
        return True
    except ImportError:
        print("✗ Flask is not installed")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def main():
    """Main startup function"""
    print("=" * 50)
    print("DebiCheck Output Reader")
    print("=" * 50)
    
    # Check if requirements are met
    if not check_requirements():
        sys.exit(1)
    
    # Check if uploads directory exists
    if not os.path.exists('uploads'):
        os.makedirs('uploads')
        print("✓ Created uploads directory")
    
    print("\nStarting DebiCheck Output Reader...")
    print("Access the application at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    # Start the Flask application
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\nError starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
