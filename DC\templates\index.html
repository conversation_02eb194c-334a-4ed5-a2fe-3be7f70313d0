{% extends "base.html" %}

{% block title %}Upload DebiCheck Output File - DebiCheck Output Reader{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Upload DebiCheck Output File
                </h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6 class="text-muted">Supported File Types:</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-success mb-2">
                                <div class="card-body text-center py-2">
                                    <i class="fas fa-file-alt text-success me-1"></i>
                                    <small><strong>Status Report</strong><br>1st & 2nd Output Files</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info mb-2">
                                <div class="card-body text-center py-2">
                                    <i class="fas fa-file-invoice text-info me-1"></i>
                                    <small><strong>Collection Request</strong><br>Output File</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning mb-2">
                                <div class="card-body text-center py-2">
                                    <i class="fas fa-file-check text-warning me-1"></i>
                                    <small><strong>Mandate Accepted</strong><br>3rd Output File</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="POST" action="{{ url_for('upload_file') }}" enctype="multipart/form-data">
                    <div class="upload-area mb-3">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>Drag and drop your file here</h5>
                        <p class="text-muted">or click to browse</p>
                        <input type="file" 
                               id="file" 
                               name="file" 
                               accept=".txt,.dat,.csv" 
                               required 
                               class="form-control d-none">
                        <button type="button" 
                                class="btn btn-outline-primary" 
                                onclick="document.getElementById('file').click()">
                            <i class="fas fa-folder-open me-2"></i>
                            Browse Files
                        </button>
                    </div>

                    <div class="file-info" style="display: none;">
                        <!-- File info will be populated by JavaScript -->
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-cogs me-2"></i>
                            Parse DebiCheck File
                        </button>
                    </div>
                </form>

                <div class="mt-4">
                    <h6 class="text-muted">File Format Information:</h6>
                    <div class="accordion" id="formatAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="statusReportHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#statusReportCollapse">
                                    Status Report Files (1st & 2nd Output, Collection Request)
                                </button>
                            </h2>
                            <div id="statusReportCollapse" class="accordion-collapse collapse" data-bs-parent="#formatAccordion">
                                <div class="accordion-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>Contains transaction status information</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Includes mandate references and debtor account details</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Shows success/failure status codes</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Provides transaction amounts and descriptions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="mandateHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#mandateCollapse">
                                    Mandate Accepted Report (3rd Output File)
                                </button>
                            </h2>
                            <div id="mandateCollapse" class="accordion-collapse collapse" data-bs-parent="#formatAccordion">
                                <div class="accordion-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>Contains accepted mandate information</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Includes creditor and debtor details</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Shows mandate status and acceptance dates</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Provides maximum amount limits</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Usage Instructions
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Supported File Formats:</h6>
                        <ul>
                            <li>.txt (Text files)</li>
                            <li>.dat (Data files)</li>
                            <li>.csv (Comma-separated values)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>File Size Limits:</h6>
                        <ul>
                            <li>Maximum file size: 16MB</li>
                            <li>Files are processed securely and deleted after parsing</li>
                            <li>No data is stored permanently</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
