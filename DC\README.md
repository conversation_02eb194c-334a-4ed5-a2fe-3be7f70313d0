# DebiCheck Output Reader

A Python web application for parsing and analyzing DebiCheck output files according to the Host to Host File Specification V5.13.

## Features

- **Multi-format Support**: Parses three types of DebiCheck output files:
  - Status Report (1st and 2nd Output Files)
  - Collection Request Output File
  - Mandate Accepted Report (3rd Output File)

- **Web Interface**: User-friendly web interface with drag-and-drop file upload
- **Comprehensive Analysis**: Detailed parsing with summary statistics and status breakdowns
- **Error Handling**: Robust error handling with detailed error messages
- **File Validation**: Supports multiple file formats (.txt, .dat, .csv) with size validation
- **Responsive Design**: Modern Bootstrap-based interface that works on all devices

## Installation

1. **Clone or download the application files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python app.py
   ```

4. **Access the application**:
   Open your web browser and navigate to `http://localhost:5000`

## Usage

### Web Interface

1. **Upload File**: 
   - Drag and drop your DebiCheck output file onto the upload area, or
   - Click "Browse Files" to select a file from your computer

2. **Parse File**: 
   - Click "Parse DebiCheck File" to process the uploaded file

3. **View Results**: 
   - Review the parsed data with summary statistics
   - Examine individual records in the detailed table
   - Toggle raw data view if needed

### Supported File Types

- **Status Report Files**: Contains transaction status information with mandate references, debtor account details, success/failure status codes, and transaction amounts
- **Collection Request Files**: Similar to status reports but specifically for collection requests
- **Mandate Accepted Reports**: Contains accepted mandate information with creditor/debtor details, mandate status, acceptance dates, and maximum amount limits

### File Format Requirements

- **File Extensions**: .txt, .dat, .csv
- **Maximum Size**: 16MB
- **Encoding**: UTF-8, Latin-1, CP1252, or ISO-8859-1
- **Format**: Must follow DebiCheck Host to Host File Specification V5.13

## API Usage

The application also provides a REST API endpoint for programmatic access:

```bash
curl -X POST -F "file=@your_debicheck_file.txt" http://localhost:5000/api/parse
```

Response format:
```json
{
  "success": true,
  "filename": "your_file.txt",
  "result": {
    "file_type": "STATUS_REPORT_1ST",
    "file_type_description": "Status Report - 1st Output File",
    "header": {...},
    "records": [...],
    "summary": {...}
  }
}
```

## File Structure

```
DC/
├── app.py                          # Main Flask application
├── debicheck_parser.py             # DebiCheck file parser
├── requirements.txt                # Python dependencies
├── README.md                       # This file
├── templates/                      # HTML templates
│   ├── base.html                   # Base template
│   ├── index.html                  # Upload page
│   └── results.html                # Results display page
└── uploads/                        # Temporary upload directory (auto-created)
```

## Configuration

The application can be configured by modifying the following variables in `app.py`:

- `UPLOAD_FOLDER`: Directory for temporary file uploads (default: 'uploads')
- `ALLOWED_EXTENSIONS`: Allowed file extensions (default: {'txt', 'dat', 'csv'})
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 16MB)

## Security Features

- **File Type Validation**: Only allows specific file extensions
- **File Size Limits**: Prevents upload of excessively large files
- **Secure Filenames**: Uses secure filename generation
- **Temporary Storage**: Uploaded files are deleted immediately after processing
- **No Persistent Storage**: No data is stored permanently on the server

## Error Handling

The application provides comprehensive error handling for:

- Invalid file formats
- Corrupted or unreadable files
- Files that don't match DebiCheck specification
- Network and server errors
- File size and permission issues

## Troubleshooting

### Common Issues

1. **"Unknown file type" error**: 
   - Ensure your file follows the DebiCheck specification
   - Check that the file contains proper headers
   - Verify the file isn't corrupted

2. **"File too large" error**: 
   - Files must be under 16MB
   - Consider splitting large files if necessary

3. **"Unable to read file" error**: 
   - Check file encoding (should be UTF-8, Latin-1, CP1252, or ISO-8859-1)
   - Ensure file isn't corrupted or password-protected

### Getting Help

If you encounter issues:

1. Check the error message displayed in the web interface
2. Review the raw file content using the "Toggle Raw Data" feature
3. Ensure your file matches the DebiCheck Host to Host File Specification V5.13
4. Verify file permissions and accessibility

## Technical Details

- **Framework**: Flask (Python web framework)
- **Frontend**: Bootstrap 5 with responsive design
- **File Processing**: Custom parser based on DebiCheck specification
- **Supported Formats**: Fixed-width, delimited (comma, pipe, tab)
- **Character Encoding**: Multi-encoding support with fallback options

## License

This application is provided as-is for parsing DebiCheck output files according to the official specification.
