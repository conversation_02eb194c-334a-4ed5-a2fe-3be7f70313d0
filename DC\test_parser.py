#!/usr/bin/env python3
"""
Test script for DebiCheck Parser
"""

from debicheck_parser import <PERSON>biCheckParser
import json

def test_parser():
    """Test the DebiCheck parser with sample files"""
    parser = DebiCheckParser()
    
    print("=" * 60)
    print("DebiCheck Parser Test")
    print("=" * 60)
    
    # Test files
    test_files = [
        'sample_files/sample_status_report.txt',
        'sample_files/sample_mandate_accepted.txt',
        'sample_files/OUTPUT.071316301545',
        'sample_files/OUTPUT.071316451545',
        'sample_files/OUTPUT.070409364588'
    ]
    
    for test_file in test_files:
        print(f"\nTesting file: {test_file}")
        print("-" * 40)
        
        try:
            result = parser.parse_file(test_file)
            
            if 'error' in result:
                print(f"❌ Error: {result['error']}")
            else:
                print(f"✅ File Type: {result['file_type_description']}")
                print(f"📊 Summary:")
                
                if result['file_type'] in ['MANDATE_ACCEPTED', 'OUTPUT_3RD']:
                    summary = result['summary']
                    print(f"   - Total Mandates: {summary['total_mandates']}")
                    print(f"   - Accepted: {summary['accepted_mandates']}")
                    print(f"   - Rejected: {summary['rejected_mandates']}")
                    print(f"   - Total Max Amount: R{summary['total_maximum_amount']:.2f}")
                else:
                    summary = result['summary']
                    print(f"   - Total Records: {summary['total_records']}")
                    print(f"   - Successful: {summary['successful_records']}")
                    print(f"   - Failed: {summary['failed_records']}")
                    print(f"   - Total Amount: R{summary['total_amount']:.2f}")
                
                print(f"📋 Status Breakdown:")
                for status, count in result['summary']['status_breakdown'].items():
                    print(f"   - {status}: {count}")
                    
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print("Test completed!")

if __name__ == "__main__":
    test_parser()
