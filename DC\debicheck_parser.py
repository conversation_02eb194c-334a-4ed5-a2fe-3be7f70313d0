"""
DebiCheck Output File Parser
Parses DebiCheck output files according to Host to Host File Specification V5.13
Handles Status Reports (1st/2nd output files) and Mandate Accepted Reports (3rd output file)
"""

import re
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DebiCheckParser:
    """Parser for DebiCheck output files"""
    
    def __init__(self):
        self.file_types = {
            'OUTPUT_1ST': '1st Output File - From Absa Payments System',
            'OUTPUT_2ND': '2nd Output File - From Debtor\'s Bank',
            'OUTPUT_3RD': '3rd Output File - Mandate Acceptance Report',
            'STATUS_REPORT_1ST': 'Status Report - 1st Output File',
            'STATUS_REPORT_2ND': 'Status Report - 2nd Output File',
            'COLLECTION_REQUEST': 'Collection Request Output File',
            'MANDATE_ACCEPTED': 'Mandate Accepted Report - 3rd Output File'
        }

        self.output_descriptions = {
            'OUTPUT_1ST': 'This file originates from the Absa Payments System and is sent back to the Corporate Client via the Link Direct Corporate Channel',
            'OUTPUT_2ND': 'This file comes from the Debtor\'s Bank and is sent to the Corporate Client',
            'OUTPUT_3RD': 'This is specifically the Mandate Acceptance Report, which is provided to the Corporate Client. This report is specific to 0227 DebiCheck mandates (and 0997 for RM mandates for amendments) and indicates whether a mandate was successfully authenticated by the debtor.'
        }
        
    def parse_file(self, filepath: str) -> Dict[str, Any]:
        """Main method to parse a DebiCheck output file"""
        try:
            # Validate file exists and is readable
            if not os.path.exists(filepath):
                return {
                    'error': 'File does not exist',
                    'file_type': 'ERROR'
                }

            # Check file size (max 16MB)
            file_size = os.path.getsize(filepath)
            if file_size > 16 * 1024 * 1024:
                return {
                    'error': 'File too large (maximum 16MB allowed)',
                    'file_type': 'ERROR'
                }

            if file_size == 0:
                return {
                    'error': 'File is empty',
                    'file_type': 'ERROR'
                }

            # Try different encodings
            content = None
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(filepath, 'r', encoding=encoding) as file:
                        content = file.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                return {
                    'error': 'Unable to read file - unsupported encoding',
                    'file_type': 'ERROR'
                }

            # Basic content validation
            if not content.strip():
                return {
                    'error': 'File contains no readable content',
                    'file_type': 'ERROR'
                }

            # Detect file type and parse accordingly
            file_type = self._detect_file_type(content)

            if file_type in ['OUTPUT_1ST', 'OUTPUT_2ND', 'STATUS_REPORT_1ST', 'STATUS_REPORT_2ND', 'COLLECTION_REQUEST']:
                return self._parse_status_report(content, file_type)
            elif file_type in ['OUTPUT_3RD', 'MANDATE_ACCEPTED']:
                return self._parse_mandate_accepted_report(content, file_type)
            else:
                return {
                    'error': 'Unknown file type - file does not match expected DebiCheck output format',
                    'file_type': 'UNKNOWN',
                    'raw_content': content[:500] + '...' if len(content) > 500 else content,
                    'suggestions': [
                        'Ensure the file is a DebiCheck output file',
                        'Check that the file contains proper headers',
                        'Verify the file format matches the specification'
                    ]
                }

        except PermissionError:
            return {
                'error': 'Permission denied - unable to read file',
                'file_type': 'ERROR'
            }
        except Exception as e:
            logger.error(f"Error parsing file: {str(e)}")
            return {
                'error': f'Unexpected error parsing file: {str(e)}',
                'file_type': 'ERROR'
            }
    
    def _detect_file_type(self, content: str) -> str:
        """Detect the type of DebiCheck output file"""
        lines = content.strip().split('\n')
        content_upper = content.upper()

        # Check for 3rd Output File (Mandate Acceptance Report) indicators
        mandate_indicators = [
            'MANDATE ACCEPTED', 'ACCEPTED MANDATE', 'MANDATE ACCEPTANCE',
            'MANDATE STATUS', 'CREDITOR REFERENCE', 'DEBTOR AUTHENTICATION',
            '0227', 'DEBICHECK MANDATE'
        ]

        mandate_score = sum(1 for indicator in mandate_indicators if indicator in content_upper)

        # Check for transaction/payment indicators (1st and 2nd output files)
        transaction_indicators = [
            'TRANSACTION', 'PAYMENT', 'COLLECTION', 'DEBIT ORDER',
            'AMOUNT', 'STATUS CODE', 'PROCESSING', 'SETTLEMENT'
        ]

        transaction_score = sum(1 for indicator in transaction_indicators if indicator in content_upper)

        # Look for specific header indicators in first 15 lines
        for line in lines[:15]:
            line = line.strip().upper()

            # Explicit 3rd output file indicators
            if any(indicator in line for indicator in ['MANDATE ACCEPTED', 'MANDATE ACCEPTANCE', '3RD OUTPUT']):
                return 'OUTPUT_3RD'

            # Explicit 1st output file indicators
            if any(indicator in line for indicator in ['1ST OUTPUT', 'ABSA PAYMENTS', 'LINK DIRECT']):
                return 'OUTPUT_1ST'

            # Explicit 2nd output file indicators
            if any(indicator in line for indicator in ['2ND OUTPUT', 'DEBTOR BANK', 'DEBTOR\'S BANK']):
                return 'OUTPUT_2ND'

        # Analyze content structure to determine file type
        if mandate_score >= 3 or self._has_mandate_structure(content):
            return 'OUTPUT_3RD'
        elif transaction_score >= 3:
            # Try to distinguish between 1st and 2nd output based on additional indicators
            if any(indicator in content_upper for indicator in ['ABSA', 'PAYMENTS SYSTEM', 'CORPORATE CHANNEL']):
                return 'OUTPUT_1ST'
            elif any(indicator in content_upper for indicator in ['DEBTOR BANK', 'BANK RESPONSE']):
                return 'OUTPUT_2ND'
            else:
                return 'OUTPUT_1ST'  # Default to 1st output for transaction files
        else:
            return 'OUTPUT_1ST'  # Default fallback
    
    def _has_mandate_structure(self, content: str) -> bool:
        """Check if content has mandate accepted report structure"""
        # Look for mandate-specific fields
        mandate_indicators = [
            'MANDATE REFERENCE',
            'CREDITOR REFERENCE', 
            'DEBTOR ACCOUNT',
            'MANDATE STATUS',
            'ACCEPTANCE DATE'
        ]
        
        content_upper = content.upper()
        found_indicators = sum(1 for indicator in mandate_indicators if indicator in content_upper)
        
        return found_indicators >= 3  # If we find 3+ mandate indicators, likely a mandate report
    
    def _parse_status_report(self, content: str, file_type: str) -> Dict[str, Any]:
        """Parse Status Report files (1st/2nd output files and Collection Request)"""
        result = {
            'file_type': file_type,
            'file_type_description': self.file_types.get(file_type, 'Status Report'),
            'output_description': self.output_descriptions.get(file_type, ''),
            'header': {},
            'records': [],
            'summary': {},
            'raw_content': content
        }

        lines = content.strip().split('\n')

        # Parse header information from 000L record
        header_info = self._extract_header_info(lines)
        result['header'] = header_info

        # Extract transaction records by grouping related records
        transaction_records = self._extract_transaction_records(lines)
        result['records'] = transaction_records

        # Generate summary
        result['summary'] = self._generate_status_summary(result['records'])

        return result

    def _parse_mandate_accepted_report(self, content: str, file_type: str = 'OUTPUT_3RD') -> Dict[str, Any]:
        """Parse Mandate Accepted Report (3rd output file)"""
        result = {
            'file_type': file_type,
            'file_type_description': self.file_types.get(file_type, 'Mandate Accepted Report'),
            'output_description': self.output_descriptions.get(file_type, ''),
            'header': {},
            'mandates': [],
            'summary': {},
            'raw_content': content
        }

        lines = content.strip().split('\n')

        # Parse header information
        header_info = self._parse_header(lines[:10])
        result['header'] = header_info

        # Parse mandate records
        data_lines = [line for line in lines if line.strip() and not self._is_header_line(line)]

        for line_num, line in enumerate(data_lines, 1):
            if line.strip():
                mandate = self._parse_mandate_record(line, line_num)
                if mandate:
                    result['mandates'].append(mandate)

        # Generate summary
        result['summary'] = self._generate_mandate_summary(result['mandates'])

        return result

    def _parse_header(self, header_lines: List[str]) -> Dict[str, Any]:
        """Parse header information from the first few lines"""
        header = {}

        for line in header_lines:
            line = line.strip()
            if not line:
                continue

            # Look for common header patterns
            if 'DATE' in line.upper():
                date_match = re.search(r'(\d{2}/\d{2}/\d{4}|\d{4}-\d{2}-\d{2}|\d{8})', line)
                if date_match:
                    header['date'] = date_match.group(1)

            if 'TIME' in line.upper():
                time_match = re.search(r'(\d{2}:\d{2}:\d{2}|\d{6})', line)
                if time_match:
                    header['time'] = time_match.group(1)

            if 'BANK' in line.upper():
                header['bank_info'] = line

            if 'REFERENCE' in line.upper():
                header['reference'] = line

        return header

    def _is_header_line(self, line: str) -> bool:
        """Check if a line is part of the header"""
        line_upper = line.upper().strip()

        header_indicators = [
            'DEBICHECK', 'STATUS REPORT', 'MANDATE ACCEPTED',
            'DATE:', 'TIME:', 'BANK:', 'REFERENCE:',
            '====', '----', '***'
        ]

        return any(indicator in line_upper for indicator in header_indicators)

    def _parse_status_record(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Parse a single status record line"""
        try:
            # Split line by common delimiters (comma, pipe, tab, fixed width)
            if '|' in line:
                fields = [field.strip() for field in line.split('|')]
            elif ',' in line:
                fields = [field.strip() for field in line.split(',')]
            elif '\t' in line:
                fields = [field.strip() for field in line.split('\t')]
            else:
                # Try fixed-width parsing (common field lengths)
                fields = self._parse_fixed_width_status(line)

            if len(fields) < 3:  # Minimum expected fields
                return None

            record = {
                'line_number': line_num,
                'raw_line': line,
                'fields': fields
            }

            # Map fields to meaningful names based on record type
            if len(fields) >= 2:
                record_type = fields[0] if fields[0] else ''
                record['record_type'] = record_type

                if record_type == '000L':
                    record.update({
                        'record_length': fields[1] if len(fields) > 1 else '',
                        'datetime_stamp': fields[2] if len(fields) > 2 else '',
                        'file_type': fields[3] if len(fields) > 3 else '',
                        'creditor_name': fields[4] if len(fields) > 4 else '',
                        'creditor_reference': fields[6] if len(fields) > 6 else ''
                    })
                elif record_type == '080L':
                    record.update({
                        'dispute_reference': fields[2] if len(fields) > 2 else '',
                        'dispute_description': fields[3] if len(fields) > 3 else ''
                    })
                elif record_type == '081L':
                    record.update({
                        'action_date': fields[2] if len(fields) > 2 else '',
                        'datetime_stamp': fields[3] if len(fields) > 3 else '',
                        'transaction_reference': fields[4] if len(fields) > 4 else '',
                        'processing_date': fields[5] if len(fields) > 5 else '',
                        'creditor_reference': fields[6] if len(fields) > 6 else '',
                        'mandate_reference': fields[7] if len(fields) > 7 else '',
                        'reason_description': fields[9] if len(fields) > 9 else '',
                        'amount': fields[10] if len(fields) > 10 else ''
                    })
                elif record_type == '0821':
                    record.update({
                        'creditor_reference': fields[3] if len(fields) > 3 else '',
                        'creditor_name': fields[4] if len(fields) > 4 else '',
                        'mandate_reference': fields[6] if len(fields) > 6 else '',
                        'collection_date': fields[7] if len(fields) > 7 else '',
                        'amount': fields[9] if len(fields) > 9 else '',
                        'currency': fields[10] if len(fields) > 10 else '',
                        'debtor_reference': fields[12] if len(fields) > 12 else '',
                        'creditor_abbreviation': fields[13] if len(fields) > 13 else ''
                    })
                elif record_type == '0822':
                    record.update({
                        'collection_date': fields[2] if len(fields) > 2 else '',
                        'due_date': fields[3] if len(fields) > 3 else ''
                    })
                elif record_type == '0823':
                    record.update({
                        'debtor_title': fields[1] if len(fields) > 1 else '',
                        'debtor_name': fields[2] if len(fields) > 2 else '',
                        'debtor_surname': fields[3] if len(fields) > 3 else '',
                        'debtor_account': fields[4] if len(fields) > 4 else '',
                        'debtor_bank_code': fields[5] if len(fields) > 5 else '',
                        'debtor_id': fields[6] if len(fields) > 6 else '',
                        'debtor_reference': fields[7] if len(fields) > 7 else ''
                    })
                elif record_type == '0824':
                    record.update({
                        'tracking_reference': fields[2] if len(fields) > 2 else '',
                        'sms_indicator': fields[3] if len(fields) > 3 else ''
                    })
                elif record_type == '085L':
                    record.update({
                        'reason_code': fields[3] if len(fields) > 3 else '',
                        'reason_description': fields[4] if len(fields) > 4 else ''
                    })
                elif record_type == '084L':
                    record.update({
                        'number_of_disputes': fields[1] if len(fields) > 1 else ''
                    })
                elif record_type == '999L':
                    record.update({
                        'number_of_records': fields[1] if len(fields) > 1 else ''
                    })
                else:
                    # Generic field mapping for unknown record types
                    record.update({
                        'transaction_reference': fields[0] if fields[0] else '',
                        'mandate_reference': fields[1] if len(fields) > 1 else '',
                        'debtor_account': fields[2] if len(fields) > 2 else '',
                        'amount': fields[3] if len(fields) > 3 else '',
                        'status_code': fields[4] if len(fields) > 4 else '',
                        'status_description': fields[5] if len(fields) > 5 else ''
                    })

            return record

        except Exception as e:
            logger.warning(f"Error parsing status record at line {line_num}: {str(e)}")
            return {
                'line_number': line_num,
                'raw_line': line,
                'error': str(e)
            }

    def _parse_mandate_record(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Parse a single mandate record line"""
        try:
            # Split line by common delimiters
            if '|' in line:
                fields = [field.strip() for field in line.split('|')]
            elif ',' in line:
                fields = [field.strip() for field in line.split(',')]
            elif '\t' in line:
                fields = [field.strip() for field in line.split('\t')]
            else:
                # Try fixed-width parsing for mandate records
                fields = self._parse_fixed_width_mandate(line)

            if len(fields) < 4:  # Minimum expected fields for mandate
                return None

            record = {
                'line_number': line_num,
                'raw_line': line,
                'fields': fields
            }

            # Map fields to meaningful names for mandate records
            if len(fields) >= 8:
                record.update({
                    'mandate_reference': fields[0] if fields[0] else '',
                    'creditor_reference': fields[1] if fields[1] else '',
                    'debtor_account': fields[2] if fields[2] else '',
                    'debtor_name': fields[3] if fields[3] else '',
                    'maximum_amount': fields[4] if fields[4] else '',
                    'mandate_status': fields[5] if fields[5] else '',
                    'acceptance_date': fields[6] if fields[6] else '',
                    'effective_date': fields[7] if fields[7] else ''
                })

            return record

        except Exception as e:
            logger.warning(f"Error parsing mandate record at line {line_num}: {str(e)}")
            return {
                'line_number': line_num,
                'raw_line': line,
                'error': str(e)
            }

    def _parse_fixed_width_status(self, line: str) -> List[str]:
        """Parse fixed-width status record according to DebiCheck specification"""
        # Parse based on record type identifier
        if len(line) < 4:
            return [line]

        record_type = line[0:4]

        if record_type == '000L':
            # Header record
            return self._parse_000L_record(line)
        elif record_type == '080L':
            # Dispute header record
            return self._parse_080L_record(line)
        elif record_type == '081L':
            # Transaction header record
            return self._parse_081L_record(line)
        elif record_type == '082L':
            # STATUS USER SET TRANSACTION RECORD: LINE "01"
            return self._parse_082L_record(line)
        elif record_type == '0821':
            # Transaction detail record (first part)
            return self._parse_0821_record(line)
        elif record_type == '0822':
            # Transaction detail record (second part)
            return self._parse_0822_record(line)
        elif record_type == '0823':
            # Debtor details record
            return self._parse_0823_record(line)
        elif record_type == '0824':
            # Additional transaction details
            return self._parse_0824_record(line)
        elif record_type == '085L':
            # Reason code record
            return self._parse_085L_record(line)
        elif record_type == '084L':
            # Trailer record for dispute
            return self._parse_084L_record(line)
        elif record_type == '999L':
            # File trailer record
            return self._parse_999L_record(line)
        else:
            # Unknown record type
            return [record_type, line[4:].strip()]

    def _parse_fixed_width_mandate(self, line: str) -> List[str]:
        """Parse fixed-width mandate record (adjust field positions based on specification)"""
        # These are example field positions - adjust based on actual specification
        field_positions = [
            (0, 20),    # Mandate Reference
            (20, 40),   # Creditor Reference
            (40, 60),   # Debtor Account
            (60, 100),  # Debtor Name
            (100, 115), # Maximum Amount
            (115, 125), # Mandate Status
            (125, 135), # Acceptance Date
            (135, 145)  # Effective Date
        ]

        fields = []
        for start, end in field_positions:
            if start < len(line):
                field = line[start:end].strip()
                fields.append(field)
            else:
                fields.append('')

        return fields

    def _generate_status_summary(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for status records"""
        summary = {
            'total_records': len(records),
            'successful_records': 0,
            'failed_records': 0,
            'error_records': 0,
            'status_breakdown': {},
            'total_amount': 0.0
        }

        for record in records:
            if 'error' in record:
                summary['error_records'] += 1
                continue

            # Count status codes
            status_code = record.get('status_code', 'UNKNOWN')
            summary['status_breakdown'][status_code] = summary['status_breakdown'].get(status_code, 0) + 1

            # Determine success/failure (adjust based on actual status codes)
            if status_code in ['00', '0', 'SUCCESS', 'ACCEPTED']:
                summary['successful_records'] += 1
            else:
                summary['failed_records'] += 1

            # Sum amounts if available
            amount_str = record.get('amount', '0')
            try:
                amount = float(amount_str.replace(',', '').replace('R', '').strip())
                summary['total_amount'] += amount
            except (ValueError, AttributeError):
                pass

        return summary

    def _generate_mandate_summary(self, mandates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for mandate records"""
        summary = {
            'total_mandates': len(mandates),
            'accepted_mandates': 0,
            'rejected_mandates': 0,
            'error_records': 0,
            'status_breakdown': {},
            'total_maximum_amount': 0.0
        }

        for mandate in mandates:
            if 'error' in mandate:
                summary['error_records'] += 1
                continue

            # Count mandate statuses
            status = mandate.get('mandate_status', 'UNKNOWN')
            summary['status_breakdown'][status] = summary['status_breakdown'].get(status, 0) + 1

            # Determine accepted/rejected
            if status.upper() in ['ACCEPTED', 'ACTIVE', 'APPROVED']:
                summary['accepted_mandates'] += 1
            else:
                summary['rejected_mandates'] += 1

            # Sum maximum amounts if available
            amount_str = mandate.get('maximum_amount', '0')
            try:
                amount = float(amount_str.replace(',', '').replace('R', '').strip())
                summary['total_maximum_amount'] += amount
            except (ValueError, AttributeError):
                pass

        return summary

    def _parse_000L_record(self, line: str) -> List[str]:
        """Parse 000L header record"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '000L',         # Record type
            line[4:5],      # Record length indicator
            line[5:17],     # Date/time stamp
            line[17:19],    # File type
            line[19:51],    # Creditor name
            line[51:52],    # Filler
            line[52:64],    # Creditor reference
            line[64:160]    # Filler
        ]

    def _parse_080L_record(self, line: str) -> List[str]:
        """Parse 080L dispute header record"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '080L',         # Record type
            line[4:5],      # Record length indicator
            line[5:16],     # Dispute reference
            line[16:80],    # Dispute description
            line[80:160]    # Filler
        ]

    def _parse_081L_record(self, line: str) -> List[str]:
        """Parse 081L transaction header record"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '081L',         # Record type
            line[4:5],      # Record length indicator
            line[5:9],      # Action date
            line[9:21],     # Date/time stamp
            line[21:36],    # Transaction reference
            line[36:44],    # Processing date
            line[44:55],    # Creditor reference
            line[55:67],    # Mandate reference
            line[67:75],    # Action date
            line[75:107],   # Reason description
            line[107:122],  # Amount
            line[122:160]   # Filler
        ]

    def _parse_082L_record(self, line: str) -> List[str]:
        """Parse 082L STATUS USER SET TRANSACTION RECORD: LINE "01" """
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '082L',         # Record type
            line[4:8],      # Line number (1501)
            line[8:20],     # Creditor reference
            line[20:52],    # Creditor name
            line[52:64],    # Creditor bank code
            line[64:76],    # Mandate reference
            line[76:84],    # Collection date
            line[84:92],    # Filler
            line[92:96],    # Status code
            line[96:128],   # Creditor abbreviation
            line[128:143],  # Amount
            line[143:146],  # Currency
            line[146:150],  # Filler
            line[150:151],  # Filler
            line[151:159],  # Processing date
            line[159:160]   # Filler
        ]

    def _parse_0821_record(self, line: str) -> List[str]:
        """Parse 0821 transaction detail record (first part)"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '0821',         # Record type
            line[4:5],      # Record length indicator
            line[5:9],      # Filler
            line[9:20],     # Creditor reference
            line[20:52],    # Creditor name
            line[52:57],    # Creditor reference
            line[57:68],    # Mandate reference
            line[68:76],    # Collection date
            line[76:108],   # Filler
            line[108:123],  # Amount
            line[123:126],  # Currency
            line[126:130],  # Filler
            line[130:146],  # Debtor reference
            line[146:160]   # Creditor abbreviation
        ]

    def _parse_08202_record(self, line: str) -> List[str]:
        """Parse 08202 STATUS USER SET TRANSACTION RECORD: LINE "02" """
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '08202',        # Record type
            line[5:15],     # Collection date
            line[15:25],    # Due date
            line[25:36],    # Creditor name
            line[36:52],    # Debtor reference
            line[52:58],    # Processing date
            line[58:160]    # Filler
        ]

    def _parse_0822_record(self, line: str) -> List[str]:
        """Parse 0822 transaction detail record (second part)"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '0822',         # Record type
            line[4:5],      # Record length indicator
            line[5:13],     # Collection date
            line[13:21],    # Due date
            line[21:160]    # Filler
        ]

    def _parse_08203_record(self, line: str) -> List[str]:
        """Parse 08203 STATUS USER SET TRANSACTION RECORD: LINE "03" """
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '08203',        # Record type
            line[5:37],     # Debtor name
            line[37:53],    # Debtor account number
            line[53:68],    # Debtor bank code
            line[68:84],    # Creditor name
            line[84:102],   # Telephone number
            line[102:118],  # Filler
            line[118:134],  # Debtor reference
            line[134:160]   # Filler
        ]

    def _parse_0823_record(self, line: str) -> List[str]:
        """Parse 0823 debtor details record"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '0823',         # Record type
            line[4:8],      # Title
            line[8:40],     # Debtor name
            line[40:72],    # Debtor surname
            line[72:87],    # Debtor account number
            line[87:102],   # Debtor bank code
            line[102:118],  # Debtor ID number
            line[118:134],  # Debtor reference
            line[134:160]   # Filler
        ]

    def _parse_08204_record(self, line: str) -> List[str]:
        """Parse 08204 STATUS USER SET TRANSACTION RECORD: LINE "04" """
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '08204',        # Record type
            line[5:37],     # Email address
            line[37:53],    # Filler
            line[53:68],    # Filler
            line[68:84],    # Filler
            line[84:99],    # Tracking reference
            line[99:100],   # SMS indicator
            line[100:160]   # Filler
        ]

    def _parse_0824_record(self, line: str) -> List[str]:
        """Parse 0824 additional transaction details"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '0824',         # Record type
            line[4:79],     # Filler
            line[79:94],    # Tracking reference
            line[94:95],    # SMS indicator
            line[95:160]    # Filler
        ]

    def _parse_085L_record(self, line: str) -> List[str]:
        """Parse 085L reason code record"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '085L',         # Record type
            line[4:5],      # Record length indicator
            line[5:9],      # Filler
            line[9:24],     # Reason code
            line[24:88],    # Reason description
            line[88:160]    # Filler
        ]

    def _parse_084L_record(self, line: str) -> List[str]:
        """Parse 084L trailer record for dispute"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '084L',         # Record type
            line[4:16],     # Number of disputes
            line[16:160]    # Filler
        ]

    def _parse_999L_record(self, line: str) -> List[str]:
        """Parse 999L file trailer record"""
        if len(line) < 160:
            line = line.ljust(160)

        return [
            '999L',         # Record type
            line[4:13],     # Number of records
            line[13:160]    # Filler
        ]

    def _extract_header_info(self, lines: List[str]) -> Dict[str, Any]:
        """Extract header information from 000L record"""
        header = {}

        for line in lines:
            if line.startswith('000L'):
                parsed = self._parse_000L_record(line)
                header.update({
                    'datetime_stamp': parsed[2],
                    'file_type': parsed[3],
                    'creditor_name': parsed[4].strip(),
                    'creditor_reference': parsed[6].strip(),
                    'processing_date': parsed[2][:8] if len(parsed[2]) >= 8 else ''
                })
                break

        return header

    def _extract_transaction_records(self, lines: List[str]) -> List[Dict[str, Any]]:
        """Extract and group transaction records into meaningful user transactions"""
        transactions = []
        current_transaction = None
        line_number = 0

        for line in lines:
            line_number += 1
            if not line.strip():
                continue

            record_type = line[:4] if len(line) >= 4 else ''

            if record_type == '082L':
                # Start of new transaction (082L STATUS USER SET TRANSACTION RECORD: LINE "01")
                if current_transaction:
                    transactions.append(current_transaction)

                parsed = self._parse_082L_record(line)
                current_transaction = {
                    'line_number': line_number,
                    'transaction_reference': '',
                    'processing_date': parsed[8].strip(),
                    'creditor_reference': parsed[2].strip(),
                    'mandate_reference': parsed[5].strip(),
                    'reason_description': '',
                    'amount': parsed[10].strip(),
                    'creditor_name': parsed[3].strip(),
                    'collection_date': parsed[6].strip(),
                    'due_date': '',
                    'currency': parsed[11].strip(),
                    'debtor_reference': '',
                    'debtor_name': '',
                    'debtor_surname': '',
                    'debtor_account': '',
                    'debtor_bank_code': parsed[4].strip(),
                    'debtor_id': '',
                    'tracking_reference': '',
                    'sms_indicator': '',
                    'reason_code': parsed[7].strip(),
                    'reason_detail': '',
                    'status_code': parsed[7].strip(),
                    'status_description': '',
                    'creditor_abbreviation': parsed[9].strip(),
                    'email_address': '',
                    'telephone_number': ''
                }

            elif record_type == '081L':
                # Main transaction header - extract transaction reference
                parsed = self._parse_081L_record(line)
                if current_transaction:
                    current_transaction.update({
                        'transaction_reference': parsed[4].strip(),
                        'processing_date': parsed[5].strip(),
                        'reason_description': parsed[9].strip()
                    })

            elif record_type == '0821' and current_transaction:
                # Transaction details (this shouldn't happen as 082L is the main record now)
                parsed = self._parse_0821_record(line)
                current_transaction.update({
                    'creditor_name': parsed[4].strip(),
                    'collection_date': parsed[7].strip(),
                    'amount': parsed[9].strip() if parsed[9].strip() else current_transaction['amount'],
                    'currency': parsed[10].strip(),
                    'debtor_reference': parsed[12].strip()
                })

            elif record_type == '0822' and current_transaction:
                # Date details
                parsed = self._parse_0822_record(line)
                current_transaction.update({
                    'collection_date': parsed[2].strip() if parsed[2].strip() else current_transaction['collection_date'],
                    'due_date': parsed[3].strip()
                })

            elif line[:5] == '08202' and current_transaction:
                # STATUS USER SET TRANSACTION RECORD: LINE "02"
                parsed = self._parse_08202_record(line)
                current_transaction.update({
                    'collection_date': parsed[1].strip() if parsed[1].strip() else current_transaction['collection_date'],
                    'due_date': parsed[2].strip(),
                    'creditor_name_line2': parsed[3].strip(),
                    'debtor_reference': parsed[4].strip(),
                    'processing_date': parsed[5].strip()
                })

            elif line[:5] == '08203' and current_transaction:
                # STATUS USER SET TRANSACTION RECORD: LINE "03"
                parsed = self._parse_08203_record(line)
                current_transaction.update({
                    'debtor_name': parsed[1].strip(),
                    'debtor_account': parsed[2].strip(),
                    'debtor_bank_code': parsed[3].strip(),
                    'creditor_name_line3': parsed[4].strip(),
                    'telephone_number': parsed[5].strip(),
                    'debtor_reference_line3': parsed[7].strip()
                })

            elif line[:5] == '08204' and current_transaction:
                # STATUS USER SET TRANSACTION RECORD: LINE "04"
                parsed = self._parse_08204_record(line)
                current_transaction.update({
                    'email_address': parsed[1].strip(),
                    'tracking_reference': parsed[5].strip(),
                    'sms_indicator': parsed[6].strip()
                })

            elif record_type == '0820' and current_transaction:
                # Handle legacy 08202, 08203, 08204 records
                if line[:5] == '08202':  # Date details
                    parsed = self._parse_0822_record(line)
                    current_transaction.update({
                        'collection_date': parsed[2].strip() if parsed[2].strip() else current_transaction['collection_date'],
                        'due_date': parsed[3].strip()
                    })
                elif line[:5] == '08203':  # Debtor details
                    parsed = self._parse_0823_record(line)
                    current_transaction.update({
                        'debtor_name': parsed[2].strip(),
                        'debtor_surname': parsed[3].strip(),
                        'debtor_account': parsed[4].strip(),
                        'debtor_bank_code': parsed[5].strip(),
                        'debtor_id': parsed[6].strip()
                    })
                elif line[:5] == '08204':  # Additional details
                    parsed = self._parse_0824_record(line)
                    current_transaction.update({
                        'tracking_reference': parsed[2].strip(),
                        'sms_indicator': parsed[3].strip()
                    })

            elif record_type == '0823' and current_transaction:
                # Debtor details
                parsed = self._parse_0823_record(line)
                current_transaction.update({
                    'debtor_name': parsed[2].strip(),
                    'debtor_surname': parsed[3].strip(),
                    'debtor_account': parsed[4].strip(),
                    'debtor_bank_code': parsed[5].strip(),
                    'debtor_id': parsed[6].strip()
                })

            elif record_type == '0824' and current_transaction:
                # Additional details
                parsed = self._parse_0824_record(line)
                current_transaction.update({
                    'tracking_reference': parsed[2].strip(),
                    'sms_indicator': parsed[3].strip()
                })

            elif record_type == '085L' and current_transaction:
                # Reason code
                parsed = self._parse_085L_record(line)
                reason_code = parsed[3].strip()
                reason_desc = parsed[4].strip()

                current_transaction.update({
                    'reason_code': reason_code,
                    'reason_detail': reason_desc,
                    'status_code': reason_code,
                    'status_description': reason_desc
                })

        # Add the last transaction
        if current_transaction:
            transactions.append(current_transaction)

        return self._format_transactions(transactions)

    def _format_transactions(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format and clean up transaction data"""
        for transaction in transactions:
            # Format amount
            if transaction['amount']:
                try:
                    # Handle DebiCheck amount format (e.g., "00000030000ZARR")
                    amount_str = transaction['amount']
                    if 'ZAR' in amount_str:
                        # Extract numeric part before ZAR
                        numeric_part = amount_str.split('ZAR')[0]
                        amount_value = float(numeric_part.lstrip('0') or '0') / 100
                        transaction['formatted_amount'] = f"R{amount_value:.2f}"
                    else:
                        # Standard format - remove leading zeros and convert from cents
                        amount_value = float(amount_str.lstrip('0') or '0') / 100
                        transaction['formatted_amount'] = f"R{amount_value:.2f}"
                except:
                    transaction['formatted_amount'] = transaction['amount']
            else:
                transaction['formatted_amount'] = "R0.00"

            # Format dates
            for date_field in ['processing_date', 'collection_date', 'due_date']:
                if transaction[date_field] and len(transaction[date_field]) == 8:
                    date_str = transaction[date_field]
                    try:
                        formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                        transaction[f"{date_field}_formatted"] = formatted_date
                    except:
                        transaction[f"{date_field}_formatted"] = transaction[date_field]
                else:
                    transaction[f"{date_field}_formatted"] = transaction[date_field]

            # Format debtor name
            debtor_full_name = f"{transaction['debtor_name']} {transaction['debtor_surname']}".strip()
            transaction['debtor_full_name'] = debtor_full_name if debtor_full_name else 'N/A'

            # Determine transaction status
            if transaction['reason_code']:
                # Common success codes
                if transaction['reason_code'] in ['00', '0000', 'SUCCESS', '']:
                    transaction['transaction_status'] = 'SUCCESS'
                    transaction['status_class'] = 'success'
                else:
                    transaction['transaction_status'] = 'FAILED'
                    transaction['status_class'] = 'danger'
            else:
                transaction['transaction_status'] = 'PENDING'
                transaction['status_class'] = 'warning'

            # Clean up empty fields
            for key, value in transaction.items():
                if isinstance(value, str) and not value.strip():
                    transaction[key] = 'N/A'

        return transactions
