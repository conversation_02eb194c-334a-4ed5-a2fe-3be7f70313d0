"""
DebiCheck Output File Parser
Parses DebiCheck output files according to Host to Host File Specification V5.13
Handles Status Reports (1st/2nd output files) and Mandate Accepted Reports (3rd output file)
"""

import re
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DebiCheckParser:
    """Parser for DebiCheck output files"""
    
    def __init__(self):
        self.file_types = {
            'STATUS_REPORT_1ST': 'Status Report - 1st Output File',
            'STATUS_REPORT_2ND': 'Status Report - 2nd Output File', 
            'COLLECTION_REQUEST': 'Collection Request Output File',
            'MANDATE_ACCEPTED': 'Mandate Accepted Report - 3rd Output File'
        }
        
    def parse_file(self, filepath: str) -> Dict[str, Any]:
        """Main method to parse a Debi<PERSON>heck output file"""
        try:
            # Validate file exists and is readable
            if not os.path.exists(filepath):
                return {
                    'error': 'File does not exist',
                    'file_type': 'ERROR'
                }

            # Check file size (max 16MB)
            file_size = os.path.getsize(filepath)
            if file_size > 16 * 1024 * 1024:
                return {
                    'error': 'File too large (maximum 16MB allowed)',
                    'file_type': 'ERROR'
                }

            if file_size == 0:
                return {
                    'error': 'File is empty',
                    'file_type': 'ERROR'
                }

            # Try different encodings
            content = None
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(filepath, 'r', encoding=encoding) as file:
                        content = file.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                return {
                    'error': 'Unable to read file - unsupported encoding',
                    'file_type': 'ERROR'
                }

            # Basic content validation
            if not content.strip():
                return {
                    'error': 'File contains no readable content',
                    'file_type': 'ERROR'
                }

            # Detect file type and parse accordingly
            file_type = self._detect_file_type(content)

            if file_type in ['STATUS_REPORT_1ST', 'STATUS_REPORT_2ND', 'COLLECTION_REQUEST']:
                return self._parse_status_report(content, file_type)
            elif file_type == 'MANDATE_ACCEPTED':
                return self._parse_mandate_accepted_report(content)
            else:
                return {
                    'error': 'Unknown file type - file does not match expected DebiCheck output format',
                    'file_type': 'UNKNOWN',
                    'raw_content': content[:500] + '...' if len(content) > 500 else content,
                    'suggestions': [
                        'Ensure the file is a DebiCheck output file',
                        'Check that the file contains proper headers',
                        'Verify the file format matches the specification'
                    ]
                }

        except PermissionError:
            return {
                'error': 'Permission denied - unable to read file',
                'file_type': 'ERROR'
            }
        except Exception as e:
            logger.error(f"Error parsing file: {str(e)}")
            return {
                'error': f'Unexpected error parsing file: {str(e)}',
                'file_type': 'ERROR'
            }
    
    def _detect_file_type(self, content: str) -> str:
        """Detect the type of DebiCheck output file"""
        lines = content.strip().split('\n')
        
        # Look for header indicators
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            
            # Check for mandate accepted report indicators
            if 'MANDATE ACCEPTED' in line.upper() or 'ACCEPTED MANDATE' in line.upper():
                return 'MANDATE_ACCEPTED'
            
            # Check for status report indicators
            if 'STATUS REPORT' in line.upper():
                if '1ST' in line.upper() or 'FIRST' in line.upper():
                    return 'STATUS_REPORT_1ST'
                elif '2ND' in line.upper() or 'SECOND' in line.upper():
                    return 'STATUS_REPORT_2ND'
                else:
                    return 'STATUS_REPORT_1ST'  # Default to 1st
            
            # Check for collection request indicators
            if 'COLLECTION REQUEST' in line.upper():
                return 'COLLECTION_REQUEST'
        
        # If no clear indicators, try to determine by structure
        if self._has_mandate_structure(content):
            return 'MANDATE_ACCEPTED'
        else:
            return 'STATUS_REPORT_1ST'  # Default fallback
    
    def _has_mandate_structure(self, content: str) -> bool:
        """Check if content has mandate accepted report structure"""
        # Look for mandate-specific fields
        mandate_indicators = [
            'MANDATE REFERENCE',
            'CREDITOR REFERENCE', 
            'DEBTOR ACCOUNT',
            'MANDATE STATUS',
            'ACCEPTANCE DATE'
        ]
        
        content_upper = content.upper()
        found_indicators = sum(1 for indicator in mandate_indicators if indicator in content_upper)
        
        return found_indicators >= 3  # If we find 3+ mandate indicators, likely a mandate report
    
    def _parse_status_report(self, content: str, file_type: str) -> Dict[str, Any]:
        """Parse Status Report files (1st/2nd output files and Collection Request)"""
        result = {
            'file_type': file_type,
            'file_type_description': self.file_types[file_type],
            'header': {},
            'records': [],
            'summary': {},
            'raw_content': content
        }
        
        lines = content.strip().split('\n')
        
        # Parse header information (typically first few lines)
        header_info = self._parse_header(lines[:10])
        result['header'] = header_info
        
        # Parse data records
        data_lines = [line for line in lines if line.strip() and not self._is_header_line(line)]
        
        for line_num, line in enumerate(data_lines, 1):
            if line.strip():
                record = self._parse_status_record(line, line_num)
                if record:
                    result['records'].append(record)
        
        # Generate summary
        result['summary'] = self._generate_status_summary(result['records'])
        
        return result

    def _parse_mandate_accepted_report(self, content: str) -> Dict[str, Any]:
        """Parse Mandate Accepted Report (3rd output file)"""
        result = {
            'file_type': 'MANDATE_ACCEPTED',
            'file_type_description': self.file_types['MANDATE_ACCEPTED'],
            'header': {},
            'mandates': [],
            'summary': {},
            'raw_content': content
        }

        lines = content.strip().split('\n')

        # Parse header information
        header_info = self._parse_header(lines[:10])
        result['header'] = header_info

        # Parse mandate records
        data_lines = [line for line in lines if line.strip() and not self._is_header_line(line)]

        for line_num, line in enumerate(data_lines, 1):
            if line.strip():
                mandate = self._parse_mandate_record(line, line_num)
                if mandate:
                    result['mandates'].append(mandate)

        # Generate summary
        result['summary'] = self._generate_mandate_summary(result['mandates'])

        return result

    def _parse_header(self, header_lines: List[str]) -> Dict[str, Any]:
        """Parse header information from the first few lines"""
        header = {}

        for line in header_lines:
            line = line.strip()
            if not line:
                continue

            # Look for common header patterns
            if 'DATE' in line.upper():
                date_match = re.search(r'(\d{2}/\d{2}/\d{4}|\d{4}-\d{2}-\d{2}|\d{8})', line)
                if date_match:
                    header['date'] = date_match.group(1)

            if 'TIME' in line.upper():
                time_match = re.search(r'(\d{2}:\d{2}:\d{2}|\d{6})', line)
                if time_match:
                    header['time'] = time_match.group(1)

            if 'BANK' in line.upper():
                header['bank_info'] = line

            if 'REFERENCE' in line.upper():
                header['reference'] = line

        return header

    def _is_header_line(self, line: str) -> bool:
        """Check if a line is part of the header"""
        line_upper = line.upper().strip()

        header_indicators = [
            'DEBICHECK', 'STATUS REPORT', 'MANDATE ACCEPTED',
            'DATE:', 'TIME:', 'BANK:', 'REFERENCE:',
            '====', '----', '***'
        ]

        return any(indicator in line_upper for indicator in header_indicators)

    def _parse_status_record(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Parse a single status record line"""
        try:
            # Split line by common delimiters (comma, pipe, tab, fixed width)
            if '|' in line:
                fields = [field.strip() for field in line.split('|')]
            elif ',' in line:
                fields = [field.strip() for field in line.split(',')]
            elif '\t' in line:
                fields = [field.strip() for field in line.split('\t')]
            else:
                # Try fixed-width parsing (common field lengths)
                fields = self._parse_fixed_width_status(line)

            if len(fields) < 3:  # Minimum expected fields
                return None

            record = {
                'line_number': line_num,
                'raw_line': line,
                'fields': fields
            }

            # Map fields to meaningful names (adjust based on actual specification)
            if len(fields) >= 6:
                record.update({
                    'transaction_reference': fields[0] if fields[0] else '',
                    'mandate_reference': fields[1] if fields[1] else '',
                    'debtor_account': fields[2] if fields[2] else '',
                    'amount': fields[3] if fields[3] else '',
                    'status_code': fields[4] if fields[4] else '',
                    'status_description': fields[5] if fields[5] else ''
                })

            return record

        except Exception as e:
            logger.warning(f"Error parsing status record at line {line_num}: {str(e)}")
            return {
                'line_number': line_num,
                'raw_line': line,
                'error': str(e)
            }

    def _parse_mandate_record(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Parse a single mandate record line"""
        try:
            # Split line by common delimiters
            if '|' in line:
                fields = [field.strip() for field in line.split('|')]
            elif ',' in line:
                fields = [field.strip() for field in line.split(',')]
            elif '\t' in line:
                fields = [field.strip() for field in line.split('\t')]
            else:
                # Try fixed-width parsing for mandate records
                fields = self._parse_fixed_width_mandate(line)

            if len(fields) < 4:  # Minimum expected fields for mandate
                return None

            record = {
                'line_number': line_num,
                'raw_line': line,
                'fields': fields
            }

            # Map fields to meaningful names for mandate records
            if len(fields) >= 8:
                record.update({
                    'mandate_reference': fields[0] if fields[0] else '',
                    'creditor_reference': fields[1] if fields[1] else '',
                    'debtor_account': fields[2] if fields[2] else '',
                    'debtor_name': fields[3] if fields[3] else '',
                    'maximum_amount': fields[4] if fields[4] else '',
                    'mandate_status': fields[5] if fields[5] else '',
                    'acceptance_date': fields[6] if fields[6] else '',
                    'effective_date': fields[7] if fields[7] else ''
                })

            return record

        except Exception as e:
            logger.warning(f"Error parsing mandate record at line {line_num}: {str(e)}")
            return {
                'line_number': line_num,
                'raw_line': line,
                'error': str(e)
            }

    def _parse_fixed_width_status(self, line: str) -> List[str]:
        """Parse fixed-width status record (adjust field positions based on specification)"""
        # These are example field positions - adjust based on actual specification
        field_positions = [
            (0, 20),    # Transaction Reference
            (20, 40),   # Mandate Reference
            (40, 60),   # Debtor Account
            (60, 75),   # Amount
            (75, 80),   # Status Code
            (80, 120)   # Status Description
        ]

        fields = []
        for start, end in field_positions:
            if start < len(line):
                field = line[start:end].strip()
                fields.append(field)
            else:
                fields.append('')

        return fields

    def _parse_fixed_width_mandate(self, line: str) -> List[str]:
        """Parse fixed-width mandate record (adjust field positions based on specification)"""
        # These are example field positions - adjust based on actual specification
        field_positions = [
            (0, 20),    # Mandate Reference
            (20, 40),   # Creditor Reference
            (40, 60),   # Debtor Account
            (60, 100),  # Debtor Name
            (100, 115), # Maximum Amount
            (115, 125), # Mandate Status
            (125, 135), # Acceptance Date
            (135, 145)  # Effective Date
        ]

        fields = []
        for start, end in field_positions:
            if start < len(line):
                field = line[start:end].strip()
                fields.append(field)
            else:
                fields.append('')

        return fields

    def _generate_status_summary(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for status records"""
        summary = {
            'total_records': len(records),
            'successful_records': 0,
            'failed_records': 0,
            'error_records': 0,
            'status_breakdown': {},
            'total_amount': 0.0
        }

        for record in records:
            if 'error' in record:
                summary['error_records'] += 1
                continue

            # Count status codes
            status_code = record.get('status_code', 'UNKNOWN')
            summary['status_breakdown'][status_code] = summary['status_breakdown'].get(status_code, 0) + 1

            # Determine success/failure (adjust based on actual status codes)
            if status_code in ['00', '0', 'SUCCESS', 'ACCEPTED']:
                summary['successful_records'] += 1
            else:
                summary['failed_records'] += 1

            # Sum amounts if available
            amount_str = record.get('amount', '0')
            try:
                amount = float(amount_str.replace(',', '').replace('R', '').strip())
                summary['total_amount'] += amount
            except (ValueError, AttributeError):
                pass

        return summary

    def _generate_mandate_summary(self, mandates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for mandate records"""
        summary = {
            'total_mandates': len(mandates),
            'accepted_mandates': 0,
            'rejected_mandates': 0,
            'error_records': 0,
            'status_breakdown': {},
            'total_maximum_amount': 0.0
        }

        for mandate in mandates:
            if 'error' in mandate:
                summary['error_records'] += 1
                continue

            # Count mandate statuses
            status = mandate.get('mandate_status', 'UNKNOWN')
            summary['status_breakdown'][status] = summary['status_breakdown'].get(status, 0) + 1

            # Determine accepted/rejected
            if status.upper() in ['ACCEPTED', 'ACTIVE', 'APPROVED']:
                summary['accepted_mandates'] += 1
            else:
                summary['rejected_mandates'] += 1

            # Sum maximum amounts if available
            amount_str = mandate.get('maximum_amount', '0')
            try:
                amount = float(amount_str.replace(',', '').replace('R', '').strip())
                summary['total_maximum_amount'] += amount
            except (ValueError, AttributeError):
                pass

        return summary
