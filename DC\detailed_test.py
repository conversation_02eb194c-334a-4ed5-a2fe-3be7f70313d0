#!/usr/bin/env python3
"""
Detailed test script for DebiCheck Parser - shows full record breakdown
"""

from debicheck_parser import DebiCheckParser
import json

def detailed_test():
    """Test the DebiCheck parser with detailed output"""
    parser = DebiCheckParser()
    
    print("=" * 80)
    print("DebiCheck Parser - Detailed Analysis")
    print("=" * 80)
    
    test_file = 'sample_files/OUTPUT.000003114317'
    
    print(f"\nAnalyzing file: {test_file}")
    print("-" * 60)
    
    try:
        result = parser.parse_file(test_file)
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return
        
        print(f"✅ File Type: {result['file_type_description']}")
        print(f"📄 Description: {result.get('output_description', 'N/A')}")
        
        print(f"\n📊 Summary:")
        summary = result['summary']
        print(f"   - Total Records: {summary['total_records']}")
        print(f"   - Successful: {summary['successful_records']}")
        print(f"   - Failed: {summary['failed_records']}")
        print(f"   - Error Records: {summary['error_records']}")
        print(f"   - Total Amount: R{summary['total_amount']:.2f}")
        
        print(f"\n📋 Status Breakdown:")
        for status, count in summary['status_breakdown'].items():
            print(f"   - {status}: {count}")
        
        print(f"\n📝 Transaction Records:")
        print("-" * 60)

        for i, record in enumerate(result['records'], 1):
            print(f"\nTransaction {i}:")
            print(f"   💳 Transaction Reference: {record.get('transaction_reference', 'N/A')}")
            print(f"   📋 Mandate Reference: {record.get('mandate_reference', 'N/A')}")
            print(f"   👤 Debtor: {record.get('debtor_full_name', 'N/A')}")
            print(f"   🏦 Debtor Account: {record.get('debtor_account', 'N/A')}")
            print(f"   🏢 Creditor: {record.get('creditor_name', 'N/A')}")
            print(f"   💰 Amount: {record.get('formatted_amount', 'N/A')}")
            print(f"   📅 Collection Date: {record.get('collection_date_formatted', 'N/A')}")
            print(f"   📅 Processing Date: {record.get('processing_date_formatted', 'N/A')}")
            print(f"   ⚠️  Status: {record.get('transaction_status', 'N/A')}")
            print(f"   🔍 Reason Code: {record.get('reason_code', 'N/A')}")
            print(f"   📝 Reason: {record.get('reason_detail', 'N/A')}")
            if record.get('email_address', 'N/A') != 'N/A':
                print(f"   📧 Email: {record.get('email_address', 'N/A')}")
            if record.get('telephone_number', 'N/A') != 'N/A':
                print(f"   📞 Phone: {record.get('telephone_number', 'N/A')}")
            if record.get('tracking_reference', 'N/A') != 'N/A':
                print(f"   📱 Tracking: {record.get('tracking_reference', 'N/A')}")
            if record.get('sms_indicator', 'N/A') != 'N/A':
                print(f"   💬 SMS: {record.get('sms_indicator', 'N/A')}")
            print(f"   📍 Line Number: {record.get('line_number', 'N/A')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("Detailed analysis completed!")

if __name__ == "__main__":
    detailed_test()
