#!/usr/bin/env python3
"""
Detailed test script for DebiCheck Parser - shows full record breakdown
"""

from debicheck_parser import DebiCheckParser
import json

def detailed_test():
    """Test the DebiCheck parser with detailed output"""
    parser = DebiCheckParser()
    
    print("=" * 80)
    print("DebiCheck Parser - Detailed Analysis")
    print("=" * 80)
    
    test_file = 'sample_files/OUTPUT.070409364588'
    
    print(f"\nAnalyzing file: {test_file}")
    print("-" * 60)
    
    try:
        result = parser.parse_file(test_file)
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return
        
        print(f"✅ File Type: {result['file_type_description']}")
        print(f"📄 Description: {result.get('output_description', 'N/A')}")
        
        print(f"\n📊 Summary:")
        summary = result['summary']
        print(f"   - Total Records: {summary['total_records']}")
        print(f"   - Successful: {summary['successful_records']}")
        print(f"   - Failed: {summary['failed_records']}")
        print(f"   - Error Records: {summary['error_records']}")
        print(f"   - Total Amount: R{summary['total_amount']:.2f}")
        
        print(f"\n📋 Status Breakdown:")
        for status, count in summary['status_breakdown'].items():
            print(f"   - {status}: {count}")
        
        print(f"\n📝 Detailed Records:")
        print("-" * 60)
        
        for i, record in enumerate(result['records'][:10], 1):  # Show first 10 records
            print(f"\nRecord {i} (Line {record['line_number']}):")
            print(f"   Record Type: {record.get('record_type', 'Unknown')}")
            
            if record.get('record_type') == '000L':
                print(f"   📋 Header Record:")
                print(f"      - DateTime: {record.get('datetime_stamp', 'N/A')}")
                print(f"      - File Type: {record.get('file_type', 'N/A')}")
                print(f"      - Creditor Name: {record.get('creditor_name', 'N/A')}")
                print(f"      - Creditor Ref: {record.get('creditor_reference', 'N/A')}")
                
            elif record.get('record_type') == '080L':
                print(f"   🔍 Dispute Header:")
                print(f"      - Dispute Ref: {record.get('dispute_reference', 'N/A')}")
                print(f"      - Description: {record.get('dispute_description', 'N/A')}")
                
            elif record.get('record_type') == '081L':
                print(f"   💳 Transaction Header:")
                print(f"      - Transaction Ref: {record.get('transaction_reference', 'N/A')}")
                print(f"      - Mandate Ref: {record.get('mandate_reference', 'N/A')}")
                print(f"      - Amount: {record.get('amount', 'N/A')}")
                print(f"      - Processing Date: {record.get('processing_date', 'N/A')}")
                print(f"      - Reason: {record.get('reason_description', 'N/A')}")
                
            elif record.get('record_type') == '0821':
                print(f"   💰 Transaction Details:")
                print(f"      - Creditor: {record.get('creditor_name', 'N/A')}")
                print(f"      - Mandate Ref: {record.get('mandate_reference', 'N/A')}")
                print(f"      - Amount: {record.get('amount', 'N/A')} {record.get('currency', 'N/A')}")
                print(f"      - Collection Date: {record.get('collection_date', 'N/A')}")
                print(f"      - Debtor Ref: {record.get('debtor_reference', 'N/A')}")
                
            elif record.get('record_type') == '0822':
                print(f"   📅 Date Details:")
                print(f"      - Collection Date: {record.get('collection_date', 'N/A')}")
                print(f"      - Due Date: {record.get('due_date', 'N/A')}")
                
            elif record.get('record_type') == '0823':
                print(f"   👤 Debtor Details:")
                print(f"      - Name: {record.get('debtor_title', '')} {record.get('debtor_name', '')} {record.get('debtor_surname', '')}")
                print(f"      - Account: {record.get('debtor_account', 'N/A')}")
                print(f"      - Bank Code: {record.get('debtor_bank_code', 'N/A')}")
                print(f"      - ID Number: {record.get('debtor_id', 'N/A')}")
                
            elif record.get('record_type') == '0824':
                print(f"   📱 Additional Details:")
                print(f"      - Tracking Ref: {record.get('tracking_reference', 'N/A')}")
                print(f"      - SMS Indicator: {record.get('sms_indicator', 'N/A')}")
                
            elif record.get('record_type') == '085L':
                print(f"   ⚠️  Reason Code:")
                print(f"      - Code: {record.get('reason_code', 'N/A')}")
                print(f"      - Description: {record.get('reason_description', 'N/A')}")
                
            elif record.get('record_type') == '084L':
                print(f"   📊 Dispute Trailer:")
                print(f"      - Number of Disputes: {record.get('number_of_disputes', 'N/A')}")
                
            elif record.get('record_type') == '999L':
                print(f"   🏁 File Trailer:")
                print(f"      - Total Records: {record.get('number_of_records', 'N/A')}")
            
            # Show raw line for reference
            print(f"   Raw: {record['raw_line'][:80]}{'...' if len(record['raw_line']) > 80 else ''}")
        
        if len(result['records']) > 10:
            print(f"\n... and {len(result['records']) - 10} more records")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("Detailed analysis completed!")

if __name__ == "__main__":
    detailed_test()
